// Cloudflare Workers 订阅代理服务
// 支持IP白名单和订阅地址管理

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;
    
    // 获取客户端IP
    const clientIP = request.headers.get('CF-Connecting-IP') || 
                    request.headers.get('X-Forwarded-For') || 
                    request.headers.get('X-Real-IP') || 
                    '127.0.0.1';

    // 路由处理
    if (path === '/admin') {
      return handleAdmin(request, env);
    } else if (path.startsWith('/api/')) {
      return handleAPI(request, env, clientIP);
    } else if (path.startsWith('/proxy/')) {
      return handleProxy(request, env, clientIP);
    } else if (path === '/whitelist-helper') {
      return handleWhitelistHelper(request);
    } else if (path === '/') {
      return new Response('订阅代理服务', { status: 200 });
    }
    
    return new Response('页面未找到', { status: 404 });
  }
};

// 处理管理页面
async function handleAdmin(request, env) {
  if (request.method === 'GET') {
    return new Response(getAdminHTML(), {
      headers: { 'Content-Type': 'text/html' }
    });
  }
  
  if (request.method === 'POST') {
    const formData = await request.formData();
    const password = formData.get('password');
    
    if (password !== env.ADMIN_PASSWORD) {
      return new Response('未授权访问', { status: 401 });
    }
    
    const action = formData.get('action');
    
    if (action === 'add_subscription') {
      const originalUrl = formData.get('original_url');
      const proxyId = generateId();
      
      await env.SUBSCRIPTION_KV.put(`sub:${proxyId}`, originalUrl);
      
      const proxyUrl = `${new URL(request.url).origin}/proxy/${proxyId}`;
      const whitelistUrl = `${new URL(request.url).origin}/api/whitelist/${proxyId}?action=add`;
      const checkUrl = `${new URL(request.url).origin}/api/whitelist/${proxyId}`;

      return new Response(JSON.stringify({
        success: true,
        proxy_url: proxyUrl,
        whitelist_url: whitelistUrl,
        check_url: checkUrl,
        proxy_id: proxyId
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    if (action === 'add_ip') {
      const proxyId = formData.get('proxy_id');
      const ip = formData.get('ip');

      // 验证输入
      if (!proxyId || !ip) {
        return new Response(JSON.stringify({
          success: false,
          error: '缺少代理ID或IP地址'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      try {
        const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
        const ipList = existingIPs ? JSON.parse(existingIPs) : [];

        if (!ipList.includes(ip)) {
          ipList.push(ip);
          await env.SUBSCRIPTION_KV.put(`whitelist:${proxyId}`, JSON.stringify(ipList));
        }

        return new Response(JSON.stringify({
          success: true,
          message: `IP ${ip} 已成功添加到代理 ${proxyId} 的白名单`,
          ip: ip,
          proxy_id: proxyId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          success: false,
          error: `添加IP失败: ${error.message}`
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
  }
  
  return new Response('不允许的请求方法', { status: 405 });
}

// 处理API请求
async function handleAPI(request, env, clientIP) {
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  
  if (pathParts[2] === 'whitelist' && pathParts[3]) {
    const proxyId = pathParts[3];
    const url = new URL(request.url);
    const action = url.searchParams.get('action');

    if (request.method === 'POST' || action === 'add') {
      // 添加当前IP到白名单
      try {
        const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
        const ipList = existingIPs ? JSON.parse(existingIPs) : [];

        if (!ipList.includes(clientIP)) {
          ipList.push(clientIP);
          await env.SUBSCRIPTION_KV.put(`whitelist:${proxyId}`, JSON.stringify(ipList));
        }

        // 如果是GET请求且action=add，返回用户友好的HTML页面
        if (request.method === 'GET' && action === 'add') {
          return new Response(getAddIPSuccessHTML(clientIP, proxyId, ipList.length), {
            headers: { 'Content-Type': 'text/html' }
          });
        }

        // 否则返回JSON响应
        return new Response(JSON.stringify({
          success: true,
          message: `IP ${clientIP} 已成功添加到代理 ${proxyId} 的白名单`,
          ip: clientIP,
          proxy_id: proxyId,
          total_ips: ipList.length
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        // 如果是GET请求且action=add，返回用户友好的错误页面
        if (request.method === 'GET' && action === 'add') {
          return new Response(getAddIPErrorHTML(clientIP, proxyId, error.message), {
            headers: { 'Content-Type': 'text/html' }
          });
        }

        return new Response(JSON.stringify({
          success: false,
          error: `添加IP到白名单失败: ${error.message}`,
          ip: clientIP,
          proxy_id: proxyId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    if (request.method === 'GET' && action !== 'add') {
      // 检查IP是否在白名单中
      try {
        const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
        const ipList = existingIPs ? JSON.parse(existingIPs) : [];

        return new Response(JSON.stringify({
          whitelisted: ipList.includes(clientIP),
          ip: clientIP,
          proxy_id: proxyId,
          total_ips: ipList.length,
          all_ips: ipList // 显示所有白名单IP用于调试
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          error: `检查白名单失败: ${error.message}`,
          ip: clientIP,
          proxy_id: proxyId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
  }
  
  return new Response('API接口未找到', { status: 404 });
}

// 处理代理请求
async function handleProxy(request, env, clientIP) {
  const url = new URL(request.url);
  const proxyId = url.pathname.split('/')[2];
  
  if (!proxyId) {
    return new Response('无效的代理ID', { status: 400 });
  }
  
  // 检查IP白名单
  const whitelistData = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
  const whitelist = whitelistData ? JSON.parse(whitelistData) : [];
  
  if (!whitelist.includes(clientIP)) {
    return new Response('访问被拒绝：IP不在白名单中', { status: 403 });
  }
  
  // 获取原始订阅地址
  const originalUrl = await env.SUBSCRIPTION_KV.get(`sub:${proxyId}`);
  
  if (!originalUrl) {
    return new Response('订阅地址未找到', { status: 404 });
  }
  
  try {
    // 代理请求到原始地址
    const response = await fetch(originalUrl, {
      method: request.method,
      headers: request.headers,
      body: request.body
    });
    
    // 返回代理响应
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers
    });
  } catch (error) {
    return new Response('代理错误: ' + error.message, { status: 500 });
  }
}

// 处理白名单助手页面
async function handleWhitelistHelper(request) {
  return new Response(getWhitelistHelperHTML(), {
    headers: { 'Content-Type': 'text/html' }
  });
}

// 生成随机ID
function generateId() {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
}

// 获取管理页面HTML
function getAdminHTML() {
  return `<!DOCTYPE html>
<html>
<head>
    <title>订阅代理管理后台</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin: 20px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <div class="container">
        <h1>订阅代理管理后台</h1>

        <h2>添加新订阅</h2>
        <form id="addSubscriptionForm">
            <div class="form-group">
                <label>管理员密码：</label>
                <input type="password" id="password" required>
            </div>
            <div class="form-group">
                <label>原始订阅地址：</label>
                <input type="url" id="originalUrl" placeholder="https://wd-red.com/subscribe/djkbm-mji2hi93" required>
            </div>
            <button type="submit">创建代理</button>
        </form>

        <div id="subscriptionResult"></div>

        <h2>添加IP到白名单</h2>
        <form id="addIPForm">
            <div class="form-group">
                <label>管理员密码：</label>
                <input type="password" id="ipPassword" required>
            </div>
            <div class="form-group">
                <label>代理ID：</label>
                <input type="text" id="proxyId" required>
            </div>
            <div class="form-group">
                <label>IP地址：</label>
                <input type="text" id="ipAddress" required>
            </div>
            <button type="submit">添加IP</button>
        </form>

        <div id="ipResult"></div>

        <h2>查看白名单</h2>
        <form id="viewWhitelistForm">
            <div class="form-group">
                <label>代理ID：</label>
                <input type="text" id="viewProxyId" required>
            </div>
            <button type="submit">查看白名单</button>
        </form>

        <div id="whitelistResult"></div>
    </div>

    <script>
        document.getElementById('addSubscriptionForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData();
            formData.append('action', 'add_subscription');
            formData.append('password', document.getElementById('password').value);
            formData.append('original_url', document.getElementById('originalUrl').value);
            
            try {
                const response = await fetch('/admin', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('subscriptionResult').innerHTML = \`
                        <div class="result success">
                            <h3>代理创建成功！</h3>
                            <p><strong>代理地址：</strong> <a href="\${result.proxy_url}" target="_blank">\${result.proxy_url}</a></p>
                            <p><strong>白名单地址（用户使用）：</strong> <a href="\${result.whitelist_url}" target="_blank">\${result.whitelist_url}</a></p>
                            <p><strong>状态检查地址：</strong> <a href="\${result.check_url}" target="_blank">\${result.check_url}</a></p>
                            <p><strong>代理ID：</strong> \${result.proxy_id}</p>
                            <div style="background: #e3f2fd; padding: 10px; margin: 10px 0; border-radius: 5px;">
                                <strong>📋 分享给用户：</strong><br>
                                用户只需点击上方的"白名单地址"即可将自己的IP添加到白名单。<br>
                                无需POST请求 - 只需简单点击！
                            </div>
                        </div>
                    \`;
                } else {
                    document.getElementById('subscriptionResult').innerHTML = \`
                        <div class="result error">创建代理时出错</div>
                    \`;
                }
            } catch (error) {
                document.getElementById('subscriptionResult').innerHTML = \`
                    <div class="result error">错误：\${error.message}</div>
                \`;
            }
        });

        document.getElementById('addIPForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'add_ip');
            formData.append('password', document.getElementById('ipPassword').value);
            formData.append('proxy_id', document.getElementById('proxyId').value);
            formData.append('ip', document.getElementById('ipAddress').value);

            try {
                const response = await fetch('/admin', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('ipResult').innerHTML = \`
                        <div class="result success">
                            IP已成功添加到白名单！<br>
                            IP地址：\${result.ip}<br>
                            代理ID：\${result.proxy_id}
                        </div>
                    \`;
                } else {
                    document.getElementById('ipResult').innerHTML = \`
                        <div class="result error">错误：\${result.error || '未知错误'}</div>
                    \`;
                }
            } catch (error) {
                document.getElementById('ipResult').innerHTML = \`
                    <div class="result error">错误：\${error.message}</div>
                \`;
            }
        });

        document.getElementById('viewWhitelistForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const proxyId = document.getElementById('viewProxyId').value;

            try {
                const response = await fetch(\`/api/whitelist/\${proxyId}\`, {
                    method: 'GET'
                });

                const result = await response.json();

                if (result.error) {
                    document.getElementById('whitelistResult').innerHTML = \`
                        <div class="result error">错误：\${result.error}</div>
                    \`;
                } else {
                    document.getElementById('whitelistResult').innerHTML = \`
                        <div class="result success">
                            <h3>代理ID的白名单：\${result.proxy_id}</h3>
                            <p><strong>您的IP：</strong> \${result.ip}</p>
                            <p><strong>是否在白名单中：</strong> \${result.whitelisted ? '是' : '否'}</p>
                            <p><strong>白名单IP总数：</strong> \${result.total_ips}</p>
                            <p><strong>所有白名单IP：</strong></p>
                            <ul>
                                \${result.all_ips.map(ip => \`<li>\${ip}</li>\`).join('')}
                            </ul>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('whitelistResult').innerHTML = \`
                    <div class="result error">错误：\${error.message}</div>
                \`;
            }
        });
    </script>
</body>
</html>`;
}

// 获取添加IP成功页面HTML
function getAddIPSuccessHTML(ip, proxyId, totalIPs) {
  return `<!DOCTYPE html>
<html>
<head>
    <title>IP添加成功</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #2e7d32; text-align: center; }
        .info { background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; text-decoration: none; display: inline-block; border-radius: 5px; margin: 5px; }
        .button:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="success">✅ IP已成功添加到白名单！</h1>

        <div class="info">
            <p><strong>您的IP地址：</strong> ${ip}</p>
            <p><strong>代理ID：</strong> ${proxyId}</p>
            <p><strong>白名单中的IP总数：</strong> ${totalIPs}</p>
        </div>

        <p>您的IP已成功添加到白名单。现在您可以访问代理地址：</p>
        <p><strong>代理地址：</strong> <a href="/proxy/${proxyId}" target="_blank">/proxy/${proxyId}</a></p>

        <div style="text-align: center; margin-top: 30px;">
            <a href="/api/whitelist/${proxyId}" class="button">查看白名单状态</a>
            <a href="/whitelist-helper" class="button">白名单助手</a>
        </div>
    </div>
</body>
</html>`;
}

// 获取添加IP错误页面HTML
function getAddIPErrorHTML(ip, proxyId, errorMessage) {
  return `<!DOCTYPE html>
<html>
<head>
    <title>添加IP错误</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error { color: #c62828; text-align: center; }
        .info { background: #ffebee; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; text-decoration: none; display: inline-block; border-radius: 5px; margin: 5px; }
        .button:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="error">❌ 添加IP到白名单时出错</h1>

        <div class="info">
            <p><strong>您的IP地址：</strong> ${ip}</p>
            <p><strong>代理ID：</strong> ${proxyId}</p>
            <p><strong>错误信息：</strong> ${errorMessage}</p>
        </div>

        <p>添加您的IP到白名单时出现错误。请重试或联系管理员。</p>

        <div style="text-align: center; margin-top: 30px;">
            <a href="/api/whitelist/${proxyId}?action=add" class="button">重试</a>
            <a href="/whitelist-helper" class="button">白名单助手</a>
        </div>
    </div>
</body>
</html>`;
}

// 获取白名单助手页面HTML
function getWhitelistHelperHTML() {
  return `<!DOCTYPE html>
<html>
<head>
    <title>白名单助手</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 20px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        .result { background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .info { background: #e3f2fd; color: #1976d2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>白名单助手</h1>
        <p>使用此页面将您的IP添加到白名单或检查您的白名单状态。</p>

        <div class="form-group">
            <label>代理ID：</label>
            <input type="text" id="proxyId" placeholder="请输入您的代理ID" required>
        </div>

        <button onclick="addCurrentIP()">将我的IP添加到白名单</button>
        <button onclick="checkWhitelistStatus()">检查白名单状态</button>
        <button onclick="generateWhitelistURL()">生成白名单链接</button>

        <div id="result"></div>

        <div class="info">
            <h3>简单方法 - 直接访问链接：</h3>
            <p>您也可以通过在浏览器中访问此链接来添加您的IP：</p>
            <p><code>/api/whitelist/您的代理ID?action=add</code></p>
            <p>只需将"您的代理ID"替换为您的实际代理ID。</p>

            <h3>使用说明：</h3>
            <ul>
                <li>在上方字段中输入您的代理ID</li>
                <li>点击"生成白名单链接"获取直接链接</li>
                <li>与用户分享此链接 - 他们只需点击即可添加自己的IP</li>
                <li>或使用下方按钮测试功能</li>
            </ul>
        </div>
    </div>

    <script>
        function generateWhitelistURL() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">请输入代理ID</div>
                \`;
                return;
            }

            const whitelistURL = \`\${window.location.origin}/api/whitelist/\${proxyId}?action=add\`;

            document.getElementById('result').innerHTML = \`
                <div class="result success">
                    <h3>白名单链接已生成！</h3>
                    <p><strong>与用户分享此链接：</strong></p>
                    <p><a href="\${whitelistURL}" target="_blank">\${whitelistURL}</a></p>
                    <p>用户只需点击此链接即可将自己的IP添加到白名单。</p>
                    <button onclick="copyToClipboard('\${whitelistURL}')" style="background: #28a745; color: white; padding: 5px 10px; border: none; cursor: pointer; border-radius: 3px;">复制链接</button>
                </div>
            \`;
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('链接已复制到剪贴板！');
            }, function(err) {
                console.error('无法复制文本: ', err);
            });
        }

        async function addCurrentIP() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">请输入代理ID</div>
                \`;
                return;
            }

            try {
                const response = await fetch(\`/api/whitelist/\${proxyId}?action=add\`, {
                    method: 'GET'
                });

                // 如果返回HTML，说明成功了
                if (response.headers.get('content-type').includes('text/html')) {
                    document.getElementById('result').innerHTML = \`
                        <div class="result success">
                            <h3>成功！</h3>
                            <p>您的IP已成功添加到白名单！</p>
                            <p>请直接访问URL查看响应结果。</p>
                        </div>
                    \`;
                } else {
                    const result = await response.json();

                    if (result.success) {
                        document.getElementById('result').innerHTML = \`
                            <div class="result success">
                                <h3>成功！</h3>
                                <p><strong>消息：</strong> \${result.message}</p>
                                <p><strong>您的IP：</strong> \${result.ip}</p>
                                <p><strong>代理ID：</strong> \${result.proxy_id}</p>
                                <p><strong>白名单中的IP总数：</strong> \${result.total_ips}</p>
                            </div>
                        \`;
                    } else {
                        document.getElementById('result').innerHTML = \`
                            <div class="result error">
                                <h3>错误</h3>
                                <p>\${result.error}</p>
                            </div>
                        \`;
                    }
                }
            } catch (error) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>错误</h3>
                        <p>添加IP失败：\${error.message}</p>
                    </div>
                \`;
            }
        }

        async function checkWhitelistStatus() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">请输入代理ID</div>
                \`;
                return;
            }

            try {
                const response = await fetch(\`/api/whitelist/\${proxyId}\`, {
                    method: 'GET'
                });

                const result = await response.json();

                if (result.error) {
                    document.getElementById('result').innerHTML = \`
                        <div class="result error">
                            <h3>错误</h3>
                            <p>\${result.error}</p>
                        </div>
                    \`;
                } else {
                    const statusClass = result.whitelisted ? 'success' : 'error';
                    const statusText = result.whitelisted ? '已在白名单中' : '不在白名单中';

                    document.getElementById('result').innerHTML = \`
                        <div class="result \${statusClass}">
                            <h3>白名单状态</h3>
                            <p><strong>状态：</strong> \${statusText}</p>
                            <p><strong>您的IP：</strong> \${result.ip}</p>
                            <p><strong>代理ID：</strong> \${result.proxy_id}</p>
                            <p><strong>白名单中的IP总数：</strong> \${result.total_ips}</p>
                            <p><strong>所有白名单IP：</strong></p>
                            <ul>
                                \${result.all_ips.map(ip => \`<li>\${ip}</li>\`).join('')}
                            </ul>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>错误</h3>
                        <p>检查状态失败：\${error.message}</p>
                    </div>
                \`;
            }
        }
    </script>
</body>
</html>`;
}
